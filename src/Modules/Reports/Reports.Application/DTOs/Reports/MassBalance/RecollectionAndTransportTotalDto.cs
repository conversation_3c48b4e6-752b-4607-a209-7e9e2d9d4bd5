using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalanceReports;

public record RecollectionAndTransportTotalDto
{
    /// <summary>
    /// Sumatoria total de las toneladas de limpieza urbana
    /// del reporte de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("all_urban_cleaning_tons")]
    public decimal AllUrbanCleaningTons { get; init; }

    /// <summary>
    /// Sumatoria total de las toneladas de barrido
    /// del reporte de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("all_sweeping_tons")]
    public decimal AllSweepingTons { get; init; }

    /// <summary>
    /// Sumatoria total de las toneladas no aprovechables
    /// del reporte de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("all_non_recyclable_tons")]
    public decimal AllNonRecyclableTons { get; init; }

    /// <summary>
    /// Sumatoria total de las toneladas de rechazo del reporte
    /// de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("all_rejection_tons")]
    public decimal AllRejectionTons { get; init; }

    /// <summary>
    /// Sumatoria total de las toneladas aprovechables del reporte
    /// de recolección y transporte
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("all_recyclable_tons")]
    public decimal AllRecyclableTons { get; init; }

    /// <summary>
    /// Sumatoria total de toneladas del reporte
    /// de recolección y transporte resultado de la suma
    /// de las toneladas de barrido y toneladas de no aprovechables
    /// </summary>
    /// <example>50.0</example>
    [JsonPropertyName("total_tons")]
    public decimal TotalTons { get; init; }
}