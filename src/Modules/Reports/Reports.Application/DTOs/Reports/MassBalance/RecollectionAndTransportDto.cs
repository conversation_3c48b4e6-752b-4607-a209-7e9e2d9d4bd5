using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalanceReports;

public record RecollectionAndTransportDto
{
    /// <summary>
    /// Toneladas de recolección y transporte por área
    /// </summary>
    [JsonPropertyName("per_area")]
    public List<RecollectionAndTransportPerAreaDto> PerArea { get; init; } = null!;

    /// <summary>
    /// Sumatorias totales de recoleccion y transporte
    /// </summary>
    [JsonPropertyName("totals")]
    public RecollectionAndTransportTotalDto Totals { get; init; } = null!;
}