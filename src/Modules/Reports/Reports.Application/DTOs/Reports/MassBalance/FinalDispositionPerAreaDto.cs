using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalanceReports;

public record FinalDispositionPerAreaDto
{
    /// <summary>
    /// Nombre de la área de prestación de servicio.
    /// </summary>
    /// <example>Medellín</example>
    [JsonPropertyName("recycling_area")]
    public string RecyclingArea { get; init; } = null!;

    /// <summary>
    /// Toneladas de descuento aplicadas en el periodo.
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("discount_tons")]
    public decimal DiscountTons { get; init; }

    /// <summary>
    /// Toneladas correspondientes unicamente a Emvarias.
    /// </summary>
    /// <example>20.5</example>
    [JsonPropertyName("emvarias_tons")]
    public decimal EmvariasTons { get; init; }

    /// <summary>
    /// Toneladas totales de la disposición final
    /// para el area de prestación de servicio.
    /// </summary>
    /// <example>50.0</example>
    [JsonPropertyName("total_tons")]
    public decimal TotalTons { get; init; }
}