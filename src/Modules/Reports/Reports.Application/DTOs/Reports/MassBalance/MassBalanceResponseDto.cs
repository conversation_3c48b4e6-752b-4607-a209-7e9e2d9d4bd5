using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Reports.MassBalanceReports;

public record MassBalanceResponseDto
{
    /// <summary>
    /// Listado por areas de prestación de servicio de la distribución de residuos.
    /// </summary>
    [JsonPropertyName("distributions")]
    public List<DistributionDto> Distributions { get; init; } = null!;

    /// <summary>
    /// Toneladas de residuos de la integracion con balanzas
    /// </summary>
    [JsonPropertyName("weighins")]
    public WeighinsDto WeighinsDto { get; init; } = null!;

    /// <summary>
    /// Toneladas de disposición final
    /// </summary>
    [JsonPropertyName("final_disposition")]
    public FinalDispositionDto FinalDispositionDto { get; init; } = null!;

    /// <summary>
    /// Toneladas de recolección y transporte
    /// </summary>
    [JsonPropertyName("recollection_and_transport")]
    public RecollectionAndTransportDto RecollectionAndTransportDto { get; init; } = null!;

    /// <summary>
    /// Indica si los reportes 14 y 34 para el periodo seleccionado son válidos
    /// </summary>
    /// <example>true</example>
    [JsonPropertyName("is_valid")]
    public bool IsValid { get; init; }
    
    /// <summary>
    /// Toneladas de recolección y transporte correspondientes a la ruta compartida
    /// entre Itagui y Emvarias (0614001)
    /// </summary>
    /// <example>14.66</example>
    [JsonPropertyName("shared_route_tons")]
    public decimal SharedRouteTons { get; init; }
}