using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Application.Common.Constants;
using Reports.Application.Common.Helpers;
using Reports.Domain;
using Reports.Domain.Common;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;
using Shared.Domain.Constants;

namespace Reports.Application.Features.Web.Queries.Reports.ExportMassBalance;

internal class ExportMassBalanceHandler : MappingService, IRequestHandler<ExportMassBalanceRequest, ExportMassBalanceResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private const int MinutesTillCacheInvalidation = 360;
    private ILookup<string, string> _areaCodeMappings = null!;

    public ExportMassBalanceHandler(
        IMapper mapper,
        IReportsUnitOfWork unitOfWork) : base(mapper)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<ExportMassBalanceResponse> Handle(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var massBalanceData = await GetMassBalanceData(request, cancellationToken);

        var period = $"{request.FromDate:yyyy/MM}";
        var excelBytes = MassBalanceExcelHelper.GenerateExcel(
            massBalanceData,
            MassBalanceExcelConstants.MainTitle,
            DateTime.Now,
            period,
            _areaCodeMappings);

        var contentType = ExcelFormatContentType.Xlsx;
        var fileName = GenerateFileName(request.FromDate, "xlsx");

        var result = Results.File(
            fileContents: excelBytes,
            contentType: contentType,
            fileDownloadName: fileName);

        return new ExportMassBalanceResponse
        {
            Result = result
        };
    }

    private async Task<MassBalance> GetMassBalanceData(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var weighinDistributions = await GetDistributions(request, cancellationToken);
        var f14 = (IReadOnlyList<ReportFormat14>)await GetReportFormat14(request, cancellationToken);
        var f34 = (IReadOnlyList<ReportFormat34>)await GetReportFormat34(request, cancellationToken);
        var weighins = (IReadOnlyList<WeighingScale>)await GetWeighins(request, cancellationToken);
        var recyclingAreas = (IReadOnlyList<RecyclingArea>)await GetRecyclingAreas(cancellationToken);

        _areaCodeMappings = recyclingAreas
            .ToLookup(area => area.Name, area => area.Code.ToString(), StringComparer.OrdinalIgnoreCase);

        return MassBalance.Create()
            .ProcessDistributions(weighinDistributions)
            .ProcessWeighins(weighins)
            .ProcessRecollectionAndTransport(f14)
            .ProcessFinalDisposition(f34, recyclingAreas)
            .Validate();
    }

    private string GenerateFileName(DateTime fromDate, string extension)
    {
        return $"{MassBalanceExcelConstants.MassBalanceFilePrefix}{fromDate:yyyyMM}.{extension}";
    }

    private async Task<IEnumerable<Distribution>> GetDistributions(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var distributions = await _unitOfWork
            .DistributionRepository
            .GetAllAsync(isPaginated: false,
                predicate: d => d.Year == request.FromDate.Year
                                && d.Month == request.FromDate.Month,
                useCache: false,
                cancellationToken: cancellationToken);

        if (distributions.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new DistributionsNotFound()));

        return distributions.Results;
    }

    private async Task<IEnumerable<ReportFormat14>> GetReportFormat14(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var reportFormat14 = await _unitOfWork
            .ReportsFormat14
            .GetAllAsync(isPaginated: false,
                predicate: r => r.VehicleArrival >= DateOnly.FromDateTime(request.FromDate)
                                && r.VehicleArrival < DateOnly.FromDateTime(request.ToDate),
                useCache: false,
                cancellationToken: cancellationToken);

        var tolls = (await _unitOfWork
            .Tolls
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken)).Results;

        var processedReport = ReportFormat14Factory
            .Create(reportFormat14.Results, tolls, DateOnly.FromDateTime(request.FromDate))
            .GetReportFormat14();

        if (!processedReport.Any())
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportFormat14NotFound()));

        return processedReport;
    }

    private async Task<IEnumerable<ReportFormat34>> GetReportFormat34(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var reportFormat34 = await _unitOfWork
            .ReportsFormat34
            .GetFilteredReportAsync(
                predicate: r => r.FilteredDate >= DateOnly.FromDateTime(request.FromDate)
                           && r.FilteredDate < DateOnly.FromDateTime(request.ToDate)
                           && r.Tons > 0,
                isPaginated: false,
                sortingOptions: new SortingOptions(SortDirection.Ascending, "FilteredDate"),
                cancellationToken: cancellationToken);

        if (!reportFormat34.Results.Any())
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportFormat34NotFound()));

        return reportFormat34.Results;
    }

    private async Task<IEnumerable<RecyclingArea>> GetRecyclingAreas(CancellationToken cancellationToken)
    {
        var recyclingAreas = await _unitOfWork
            .RecyclingAreaRepository
            .GetAllAsync(isPaginated: false,
                useCache: true,
                cacheExpirationInMinutes: MinutesTillCacheInvalidation,
                cancellationToken: cancellationToken);

        if (recyclingAreas.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new RecyclingAreaNotFound()));

        return recyclingAreas.Results;
    }

    private async Task<IEnumerable<WeighingScale>> GetWeighins(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var weighins = await _unitOfWork
            .WeighingScales
            .GetAllAsync(includes: w => w.Town,
                isPaginated: false,
                predicate: r => r.EntryDate >= request.FromDate
                                     && r.EntryDate < request.ToDate
                                     && r.CancelDate == null,
                useCache: false,
                cancellationToken: cancellationToken);

        if (weighins.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new WeighingScaleNotFound()));

        return weighins.Results;
    }
}
